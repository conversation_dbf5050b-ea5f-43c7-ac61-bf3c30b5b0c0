############################################################
# CIS 521: Homework 8
############################################################

############################################################
# Imports
import random
import string
############################################################

# Include your imports here, if any are used.

############################################################

student_name = "<PERSON> Richards"

############################################################
# Section 1: Ngram Models
############################################################


def tokenize(text):
    tokens = []
    for word in text.split():
        word = word.strip()
        contains_punctuation = False
        temp_token = ""
        for character in word:
            if character:
                if character in string.punctuation:
                    contains_punctuation = True
                    if temp_token:
                        tokens.append(temp_token)
                    tokens.append(character)
                    temp_token = ""
                else:
                    temp_token += character
        if not contains_punctuation:
            if word:
                tokens.append(word)
        else:
            if temp_token:
                tokens.append(temp_token)
    return tokens


def ngrams(n, tokens):
    padded_tokens = ["<START>"] * (n - 1) + tokens + ["<END>"]
    n_grams = []
    for index in range(len(padded_tokens) - n + 1):
        # Each n-gram should consist of a 2-element tuple
        # (context, token), where the context is itself an
        # (n−1)-element tuple comprised of the n−1
        # words preceding the current token.
        context = tuple(padded_tokens[index: index + n - 1])
        token = padded_tokens[index + n - 1]
        n_grams.append((context, token))
    return n_grams


class NgramModel(object):

    def __init__(self, n):
        self.n = n
        self.ngram_counts = {}
        self.context_counts = {}

    def update(self, sentence):
        # Compute the n-grams for the input sentence and
        # updates the internal counts.
        sentence_ngrams = ngrams(self.n, tokenize(sentence))
        for context, token in sentence_ngrams:
            if (context, token) not in self.ngram_counts:
                self.ngram_counts[(context, token)] = 0
            self.ngram_counts[(context, token)] += 1
            if context not in self.context_counts:
                self.context_counts[context] = 0
            self.context_counts[context] += 1

    def prob(self, context, token):
        if context not in self.context_counts:
            return 0
        # Conditional probability =
        # Probability of token and context / probability of context
        probability = self.ngram_counts.get((context, token), 0)\
            / self.context_counts.get(context)
        return probability

    def random_token(self, context):
        # Reference Ed Discussion posts:
        # https://edstem.org/us/courses/78274/discussion/6802719
        # https://edstem.org/us/courses/78274/discussion/6808221
        # https://edstem.org/us/courses/78274/discussion/6808292
        possible_tokens = []
        for loop_context, token in self.ngram_counts.keys():
            if loop_context == context:
                possible_tokens.append(token)
        if not possible_tokens:
            return None
        # Sort according to Python’s natural lexicographic ordering,
        # to keep the order deterministic.
        possible_tokens = sorted(possible_tokens)
        cumulative_probabilities = []
        cumulative_sum = 0
        for token in possible_tokens:
            cumulative_sum += self.prob(context, token)
            cumulative_probabilities.append(cumulative_sum)
        r = random.random()
        # Return the first token with a cumulative probability greater than r.
        for index, cumulative_prob in enumerate(cumulative_probabilities):
            if r < cumulative_prob:
                return possible_tokens[index]

    def random_text(self, token_count):
        if self.n == 1:
            # If n = 1, your context should always be the empty tuple.
            starting_context = tuple()
        else:
            # Your starting context should always be the (n−1)-tuple
            # ("<START>", ..., "<START>")
            starting_context = tuple(["<START>"] * (self.n - 1))
        context = starting_context
        tokens = []
        for _ in range(token_count):
            token = self.random_token(context)
            if not token:
                break
            tokens.append(token)
            # If n = 1 (unigram), your context should always be the empty
            # tuple. Otherwise, update the context as tokens are generated.
            if self.n == 1:
                context = tuple()
            else:
                if token == "<END>":
                    context = starting_context
                else:
                    # Add the new token and remove the left-most
                    # token, so the context remains an (n-1)-tuple (i.e. k).
                    context = context[1:] + (token,)
        return " ".join(tokens)

    def perplexity(self, sentence):
        # Ed Discussion post:
        # https://edstem.org/us/courses/78274/discussion/6802939
        sentence_ngrams = ngrams(self.n, tokenize(sentence))
        # Avoids divide-by-zero error.
        if not sentence_ngrams:
            return float("inf")
        probability_product = 1
        for context, token in sentence_ngrams:
            probability = self.prob(context, token)
            # Avoids divide-by-zero error.
            if probability == 0:
                return float("inf")
            probability_product *= probability
        ngram_count = len(sentence_ngrams)
        perplexity = (1.0 / probability_product) ** (1.0 / ngram_count)
        return perplexity


def create_ngram_model(n, path):
    model = NgramModel(n)
    with open(path, "r") as file_handler:
        for line in file_handler:
            sentence = line.strip()
            if sentence:
                model.update(sentence)
    return model

############################################################
# Section 2: Feedback
############################################################


# Just an approximation is fine.
feedback_question_1 = """
I spent about 9 hours on this assignment.
"""

feedback_question_2 = """
The most challenging part of this assignment was
implementing the `random_token()` and `random_text()` methods.
"""

feedback_question_3 = """
I enjoyed the refresher on probability theory and
seeing how it's used in language models. I also like that
instructions on generating Harry Potter fanfiction were given
(which I will attempt at a later time).
"""
